# Number of days of inactivity before an issue becomes stale
daysUntilStale: 20
# Number of days of inactivity before a stale issue is closed
daysUntilClose: 5
# Issues with these labels will never be considered stale
exemptLabels:
  - pinned
  - security
  - active
# Label to use when marking an issue as stale
staleLabel: inactive
# Comment to post when marking an issue as stale. Set to `false` to disable
markComment: >
  This issue has been automatically marked as inactive because it has not had
  activity for 20 days. It will be closed in 5 days if no further activity occurs.
  Thank you for your contributions.
# Comment to post when closing a stale issue. Set to `false` to disable
closeComment: false
