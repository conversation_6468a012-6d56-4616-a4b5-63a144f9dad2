/*global tarteaucitron */
/* min ready */
tarteaucitron.lang = {
    "middleBarHead": "☝️ 🍪",
    "adblock": "Hallo! Diese Seite ist transparent und lässt Ihnen die Wahl der externen Services, die aktiviert werden dürfen.",
    "adblock_call": "Bitte deaktivieren Sie Ihren 'Werbeblocker' um Cookie-Einstellungen vornehmen zu können.",
    "reload": "Seite neu laden",
    
    "alertBigScroll": "Durch weiterblättern,",
    "alertBigClick": "Wenn Sie diese Webseite benutzen,",
    "alertBig": "stimmen Sie der Benutzung von externen Diensten zu",
    
    "alertBigPrivacy": "Diese Webseite verwendet 'Cookies' um Inhalte und Anzeigen zu personalisieren und zu analysieren. Bestim<PERSON>, welche Dienste benutzt werden dürfen",
    "alertSmall": "Datenschutz-Einstellungen",
    "personalize": "Personalisieren",
    "acceptAll": "Alle akzeptieren",
    "close": "Schl<PERSON>ßen",
    "closeBanner": "Cookies-Banner ausblenden",

    "privacyUrl": "Datenschutzbestimmungen",
    
    "all": "Einstellungen für alle Dienste",

    "info": "Schutz der Privatsphäre",
    "disclaimer": "Wenn Sie diese Dienste nutzen, erlauben Sie deren 'Cookies' und Tracking-Funktionen, die zu ihrer ordnungsgemäßen Funktion notwendig sind.",
    "allow": "Erlauben",
    "deny": "Ablehnen",
    "noCookie": "Dieser Dienst nutzt keine 'Cookies'.",
    "useCookie": "Dieser Dienst kann 'Cookies' verwenden",
    "useCookieCurrent": "Dieser Dienst verwendet",
    "useNoCookie": "Dieser Dienst hat keine 'Cookies' installiert.",
    "more": "Weiter lesen",
    "source": "Zur offiziellen Webseite",
    "credit": "Cookie Manager von tarteaucitron.js",
    "noServices": "Diese Website verwendet keine Cookies, die Ihrer Zustimmung bedürfen.",

    "toggleInfoBox": "Zeige/Verberge Cookie-Einstellungen",
    "title": "Cookie-Einstellungen",
    "cookieDetail": "Cookie Details für",
    "ourSite": "auf unserer Seite",
    "modalWindow": "(modales Fenster)",
    "newWindow": "(neues Fenster)",
    "allowAll": "Erlaube alle Cookies",
    "denyAll": "Verbiete alle Cookies",

    "icon": "Cookies",
    
    "fallback": "ist deaktiviert.",
    "allowed": "erlaubt",
    "disallowed": "nicht erlaubt",

    "ads": {
        "title": "Werbenetzwerke",
        "details": "Werbenetzwerke können mit dem Verkauf von Werbeplatzierungen auf der Seite Einnahmen erhalten."
    },
    "analytic": {
        "title": "Besucher Zähldienste",
        "details": "Die verwendeten Besucher Zähldienste generieren Statistiken die dabei helfen, die Seite zu verbessern."
    },
    "social": {
        "title": "Soziale Netzwerke",
        "details": "Soziale Netzwerke können die Benutzbarkeit der Seite verbessern und ihren Bekanntheitsgrad erhöhen."
    },
    "video": {
        "title": "Videos",
        "details": "Videoplattformen erlauben Videoinhalte einzublenden und die Sichtbarkeit der Seite zu erhöhen."
    },
    "comment": {
        "title": "Kommentare",
        "details": "Kommentar Manager erleichtern die Organisation von Kommentaren und helfen dabei Spam zu verhindern."
    },
    "support": {
        "title": "Support",
        "details": "Support Dienste erlauben es die Urheber der Seite zu kontaktieren und sie zu verbessern."
    },
    "api": {
        "title": "APIs",
        "details": "APIs werden benutzt um Skripte zu laden, wie: Geolokalisierung, Suchmaschinen, Übersetzungen, ..."
    },
    "other": {
        "title": "Andere",
        "details": "Dienste zum Anzeigen von Web-Inhalten."
    },

    "google": {
        "title": "Spezifische Zustimmung zu Google-Diensten",
        "details": "Google kann Ihre Daten zur Messung der Zielgröße, Werbeleistung oder zur Bereitstellung personalisierter Anzeigen verwenden."
    },
    
    "mandatoryTitle": "Notwendige Cookies",
    "mandatoryText": "Diese Seite nutzt Cookies, um die Bedienung der Website zu ermöglichen, diese können nicht deaktiviert werden",

    "save": "Speichern",
    "ourpartners": "Unsere Partner"
};
