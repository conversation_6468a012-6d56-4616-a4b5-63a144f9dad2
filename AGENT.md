# Atlas Codebase Guide

## Commands
- **Build**: `npm run build` / `pnpm build`
- **Dev**: `npm run dev` / `pnpm dev` 
- **Format**: `npm run format` (Prettier)
- **Lint**: `npm run lint` (ESLint)
- **Preview**: `npm run preview`

## Architecture
- **Framework**: Astro 5 with SSR, deployed on Vercel
- **CMS**: Keystatic (`/keystatic` admin interface)
- **Styling**: TailwindCSS 4.1 with Tailwind forms/animate
- **Content**: Collections in Italian (blogIT, serviziIT, otherPagesIT, authors)
- **Fonts**: Poppins preloaded for performance

## Code Style
- **Imports**: Use path aliases (`@components/*`, `@config/*`, `@layouts/*`, `@assets/*`)
- **Formatting**: Tabs (width 2), 100 char line limit, trailing commas, double quotes
- **Components**: `.astro` files for layout/static, React (TSX) for interactive
- **Types**: TypeScript enabled, strict null checks, allow `any` and unused vars
- **Naming**: PascalCase components, camelCase variables/functions
- **File structure**: Components in folders with index files

## Notes
- No test framework configured - check with user before adding tests
- Dark mode temporarily disabled (light theme only)
- ESLint allows `@ts-ignore` and disables some strict rules for template flexibility
